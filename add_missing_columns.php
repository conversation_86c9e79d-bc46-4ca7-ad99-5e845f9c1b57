<?php
// Add Missing Columns to Tables
require_once 'db_connection.php';

echo "<h2>🔧 Adding Missing Columns</h2>";

// Add created_at column to tokenslip table
echo "<h3>Step 1: Adding created_at to tokenslip table...</h3>";
try {
    $sql = "ALTER TABLE tokenslip ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP";
    if ($conn->query($sql)) {
        echo "✅ created_at column added to tokenslip table<br>";
    } else {
        echo "⚠️ Column might already exist: " . $conn->error . "<br>";
    }
} catch (Exception $e) {
    echo "⚠️ Error: " . $e->getMessage() . "<br>";
}

// Add status column to tokenslip table if not exists
echo "<h3>Step 2: Adding status to tokenslip table...</h3>";
try {
    $sql = "ALTER TABLE tokenslip ADD COLUMN status ENUM('Pending', 'Processing', 'Completed', 'Rejected') DEFAULT 'Pending'";
    if ($conn->query($sql)) {
        echo "✅ status column added to tokenslip table<br>";
    } else {
        echo "⚠️ Column might already exist: " . $conn->error . "<br>";
    }
} catch (Exception $e) {
    echo "⚠️ Error: " . $e->getMessage() . "<br>";
}

// Create user_activity_logs table if not exists
echo "<h3>Step 3: Creating user_activity_logs table...</h3>";
try {
    $sql = "CREATE TABLE IF NOT EXISTS user_activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        action VARCHAR(100) NOT NULL,
        table_name VARCHAR(50) DEFAULT NULL,
        record_id INT DEFAULT NULL,
        old_values TEXT DEFAULT NULL,
        new_values TEXT DEFAULT NULL,
        ip_address VARCHAR(45) NOT NULL,
        user_agent TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    if ($conn->query($sql)) {
        echo "✅ user_activity_logs table created<br>";
    } else {
        echo "⚠️ Table might already exist: " . $conn->error . "<br>";
    }
} catch (Exception $e) {
    echo "⚠️ Error: " . $e->getMessage() . "<br>";
}

// Update existing records with default timestamps
echo "<h3>Step 4: Updating existing records...</h3>";
try {
    $sql = "UPDATE tokenslip SET created_at = NOW() WHERE created_at IS NULL OR created_at = '0000-00-00 00:00:00'";
    if ($conn->query($sql)) {
        echo "✅ Updated existing records with timestamps<br>";
    }
} catch (Exception $e) {
    echo "⚠️ Error updating records: " . $e->getMessage() . "<br>";
}

echo "<h3>🎉 Column Addition Complete!</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>✅ Now you can use:</strong></p>";
echo "<ul>";
echo "<li><a href='admin_dashboard.php'>Original Admin Dashboard</a></li>";
echo "<li><a href='admin_dashboard_simple.php'>Simple Admin Dashboard</a></li>";
echo "</ul>";
echo "</div>";

// Verify table structure
echo "<h3>Verification:</h3>";
$result = $conn->query("DESCRIBE tokenslip");
if ($result) {
    echo "<strong>Tokenslip table columns:</strong><br>";
    while ($row = $result->fetch_assoc()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")<br>";
    }
}

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}
</style>
