<?php
require_once 'session_multiuser.php';

// Check if user is admin
if (!isAdmin()) {
    header("Location: login_multiuser.php?error=access_denied");
    exit();
}

// Get dashboard statistics
$stats = [];

// Total users
$result = $conn->query("SELECT COUNT(*) as total FROM login WHERE status = 'active'");
$stats['total_users'] = $result->fetch_assoc()['total'];

// Total records
$result = $conn->query("SELECT COUNT(*) as total FROM tokenslip");
$stats['total_records'] = $result->fetch_assoc()['total'];

// Today's records
$result = $conn->query("SELECT COUNT(*) as total FROM tokenslip WHERE DATE(created_at) = CURDATE()");
$stats['today_records'] = $result->fetch_assoc()['total'];

// Pending records
$result = $conn->query("SELECT COUNT(*) as total FROM tokenslip WHERE status = 'Pending'");
$stats['pending_records'] = $result->fetch_assoc()['total'];

// Recent activities
$recent_activities = $conn->query("
    SELECT ual.*, l.username, l.full_name 
    FROM user_activity_logs ual 
    JOIN login l ON ual.user_id = l.id 
    ORDER BY ual.created_at DESC 
    LIMIT 10
");

// Recent users
$recent_users = $conn->query("
    SELECT username, full_name, role, created_at, last_login 
    FROM login 
    WHERE status = 'active' 
    ORDER BY created_at DESC 
    LIMIT 5
");

// System status
$system_status = [
    'database' => 'Connected',
    'users_online' => $conn->query("SELECT COUNT(*) as total FROM login WHERE last_login >= DATE_SUB(NOW(), INTERVAL 1 HOUR)")->fetch_assoc()['total'],
    'disk_usage' => '45%', // This would be calculated based on actual disk usage
    'memory_usage' => '62%' // This would be calculated based on actual memory usage
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Pakistan Identity System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5530;
            --secondary-color: #4a7c59;
            --accent-color: #f8f9fa;
            --text-dark: #2d3436;
            --border-color: #e9ecef;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 30px;
            position: relative;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-content h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
        }

        .user-info {
            text-align: right;
        }

        .user-info h5 {
            margin: 0;
            font-weight: 600;
        }

        .user-info small {
            opacity: 0.9;
        }

        .content-section {
            padding: 40px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            border-left: 4px solid;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.primary { border-left-color: var(--primary-color); }
        .stat-card.success { border-left-color: var(--success-color); }
        .stat-card.warning { border-left-color: var(--warning-color); }
        .stat-card.info { border-left-color: var(--info-color); }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 15px;
        }

        .stat-icon.primary { background: var(--primary-color); }
        .stat-icon.success { background: var(--success-color); }
        .stat-icon.warning { background: var(--warning-color); }
        .stat-icon.info { background: var(--info-color); }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-dark);
            margin: 0;
        }

        .stat-label {
            color: #6c757d;
            font-weight: 500;
            margin: 0;
        }

        .section-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 20px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .action-btn {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 12px;
            text-decoration: none;
            text-align: center;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(44, 85, 48, 0.3);
            color: white;
            text-decoration: none;
        }

        .activity-item {
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: white;
            background: var(--info-color);
        }

        .activity-content {
            flex: 1;
        }

        .activity-content h6 {
            margin: 0;
            font-weight: 600;
            color: var(--text-dark);
        }

        .activity-content small {
            color: #6c757d;
        }

        .system-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .status-item {
            text-align: center;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
        }

        .status-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--success-color);
        }

        .status-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .navigation-links {
            margin-bottom: 20px;
        }

        .navigation-links a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            margin-right: 15px;
            padding: 8px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .navigation-links a:hover {
            background: var(--accent-color);
            color: var(--secondary-color);
            text-decoration: none;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header-section {
                padding: 20px;
            }

            .header-content {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .header-content h1 {
                font-size: 2rem;
            }

            .content-section {
                padding: 25px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }
        }

        /* Animation */
        .stat-card, .section-card {
            animation: slideInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .stat-card:nth-child(2) { animation-delay: 0.2s; }
        .stat-card:nth-child(3) { animation-delay: 0.3s; }
        .stat-card:nth-child(4) { animation-delay: 0.4s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="header-content">
                <div>
                    <h1><i class="fas fa-tachometer-alt me-3"></i>Admin Dashboard</h1>
                    <p class="subtitle mb-0">Pakistan Identity Card Management System</p>
                </div>
                <div class="user-info">
                    <h5>Welcome, <?php echo htmlspecialchars($_SESSION['full_name']); ?></h5>
                    <small><i class="fas fa-crown me-1"></i>Administrator</small>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <!-- Navigation -->
            <div class="navigation-links">
                <a href="user_management.php"><i class="fas fa-users-cog me-1"></i>User Management</a>
                <a href="show_data.php"><i class="fas fa-table me-1"></i>View All Records</a>
                <a href="index.php"><i class="fas fa-plus me-1"></i>Add New Record</a>
                <a href="user_activity_logs.php"><i class="fas fa-history me-1"></i>Activity Logs</a>
                <a href="logout.php"><i class="fas fa-sign-out-alt me-1"></i>Logout</a>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon primary">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="stat-number"><?php echo $stats['total_users']; ?></h3>
                    <p class="stat-label">Total Users</p>
                </div>

                <div class="stat-card success">
                    <div class="stat-icon success">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="stat-number"><?php echo $stats['total_records']; ?></h3>
                    <p class="stat-label">Total Records</p>
                </div>

                <div class="stat-card warning">
                    <div class="stat-icon warning">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <h3 class="stat-number"><?php echo $stats['today_records']; ?></h3>
                    <p class="stat-label">Today's Records</p>
                </div>

                <div class="stat-card info">
                    <div class="stat-icon info">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="stat-number"><?php echo $stats['pending_records']; ?></h3>
                    <p class="stat-label">Pending Records</p>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-bolt"></i>
                    Quick Actions
                </h3>
                <div class="quick-actions">
                    <a href="user_management.php" class="action-btn">
                        <i class="fas fa-user-plus"></i>
                        Create New User
                    </a>
                    <a href="index.php" class="action-btn">
                        <i class="fas fa-plus-circle"></i>
                        Add New Record
                    </a>
                    <a href="show_data.php" class="action-btn">
                        <i class="fas fa-search"></i>
                        Search Records
                    </a>
                    <a href="user_activity_logs.php" class="action-btn">
                        <i class="fas fa-chart-line"></i>
                        View Reports
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Recent Activities -->
                <div class="col-lg-8">
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-history"></i>
                            Recent Activities
                        </h3>
                        <div class="activities-list">
                            <?php if ($recent_activities->num_rows > 0): ?>
                                <?php while ($activity = $recent_activities->fetch_assoc()): ?>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <i class="fas fa-<?php
                                            switch($activity['action']) {
                                                case 'login': echo 'sign-in-alt'; break;
                                                case 'create_user': echo 'user-plus'; break;
                                                case 'update_user': echo 'user-edit'; break;
                                                case 'delete_user': echo 'user-times'; break;
                                                case 'reset_password': echo 'key'; break;
                                                default: echo 'cog';
                                            }
                                        ?>"></i>
                                    </div>
                                    <div class="activity-content">
                                        <h6><?php echo htmlspecialchars($activity['full_name']); ?></h6>
                                        <small>
                                            <?php
                                            $action_text = str_replace('_', ' ', $activity['action']);
                                            echo ucfirst($action_text);
                                            if ($activity['table_name']) {
                                                echo ' in ' . $activity['table_name'];
                                            }
                                            ?>
                                            - <?php echo date('M d, Y H:i', strtotime($activity['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-history fa-3x mb-3"></i>
                                    <p>No recent activities found.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- System Status & Recent Users -->
                <div class="col-lg-4">
                    <!-- System Status -->
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-server"></i>
                            System Status
                        </h3>
                        <div class="system-status">
                            <div class="status-item">
                                <div class="status-value">
                                    <i class="fas fa-check-circle text-success"></i>
                                </div>
                                <div class="status-label">Database</div>
                            </div>
                            <div class="status-item">
                                <div class="status-value"><?php echo $system_status['users_online']; ?></div>
                                <div class="status-label">Users Online</div>
                            </div>
                            <div class="status-item">
                                <div class="status-value"><?php echo $system_status['disk_usage']; ?></div>
                                <div class="status-label">Disk Usage</div>
                            </div>
                            <div class="status-item">
                                <div class="status-value"><?php echo $system_status['memory_usage']; ?></div>
                                <div class="status-label">Memory</div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Users -->
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-user-clock"></i>
                            Recent Users
                        </h3>
                        <div class="recent-users">
                            <?php if ($recent_users->num_rows > 0): ?>
                                <?php while ($user = $recent_users->fetch_assoc()): ?>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="activity-content">
                                        <h6><?php echo htmlspecialchars($user['full_name']); ?></h6>
                                        <small>
                                            @<?php echo htmlspecialchars($user['username']); ?>
                                            (<?php echo ucfirst($user['role']); ?>)
                                            <br>
                                            Joined: <?php echo date('M d, Y', strtotime($user['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <p>No users found.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto-refresh dashboard every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);

        // Add smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.stat-card, .section-card');
            elements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    el.style.transition = 'all 0.6s ease';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // Real-time clock
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            const dateString = now.toLocaleDateString();

            // You can add a clock element if needed
            console.log(`Current time: ${timeString} ${dateString}`);
        }

        setInterval(updateClock, 1000);
    </script>
</body>
</html>
