<?php
require_once 'session.php'; // Use existing session

// Check if user is admin (if role column exists)
$user_role = isset($_SESSION['role']) ? $_SESSION['role'] : 'user';
if ($user_role !== 'admin') {
    // If not admin, redirect to regular pages
    header("Location: index.php");
    exit();
}

// Get basic statistics
$stats = [];

// Total users
try {
    $result = $conn->query("SELECT COUNT(*) as total FROM login");
    $stats['total_users'] = $result->fetch_assoc()['total'];
} catch (Exception $e) {
    $stats['total_users'] = 0;
}

// Total records
try {
    $result = $conn->query("SELECT COUNT(*) as total FROM tokenslip");
    $stats['total_records'] = $result->fetch_assoc()['total'];
} catch (Exception $e) {
    $stats['total_records'] = 0;
}

// Today's records (try different date columns)
$stats['today_records'] = 0;
$date_columns = ['created_at', 'date', 'timestamp', 'entry_date'];
foreach ($date_columns as $col) {
    try {
        $result = $conn->query("SELECT COUNT(*) as total FROM tokenslip WHERE DATE($col) = CURDATE()");
        $stats['today_records'] = $result->fetch_assoc()['total'];
        break;
    } catch (Exception $e) {
        continue;
    }
}

// Pending records (try different status columns)
$stats['pending_records'] = 0;
$status_values = ['Pending', 'pending', 'PENDING'];
foreach ($status_values as $status) {
    try {
        $result = $conn->query("SELECT COUNT(*) as total FROM tokenslip WHERE status = '$status'");
        $stats['pending_records'] = $result->fetch_assoc()['total'];
        break;
    } catch (Exception $e) {
        continue;
    }
}

// Get recent records
try {
    $recent_records = $conn->query("SELECT * FROM tokenslip ORDER BY id DESC LIMIT 5");
} catch (Exception $e) {
    $recent_records = null;
}

// Get users list
try {
    $users_list = $conn->query("SELECT id, username, role, status FROM login ORDER BY id DESC LIMIT 5");
} catch (Exception $e) {
    try {
        $users_list = $conn->query("SELECT id, username FROM login ORDER BY id DESC LIMIT 5");
    } catch (Exception $e2) {
        $users_list = null;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Pakistan Identity System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header-card {
            background: linear-gradient(135deg, #2c5530 0%, #4a7c59 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.primary { border-left-color: #2c5530; }
        .stat-card.success { border-left-color: #28a745; }
        .stat-card.warning { border-left-color: #ffc107; }
        .stat-card.info { border-left-color: #17a2b8; }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .stat-label {
            color: #666;
            font-weight: 500;
            margin-top: 5px;
        }

        .action-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .action-btn {
            background: linear-gradient(135deg, #2c5530 0%, #4a7c59 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(44, 85, 48, 0.3);
            color: white;
            text-decoration: none;
        }

        .table-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #333;
        }

        .table td {
            border: none;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }

        .badge {
            font-size: 0.8rem;
            padding: 6px 12px;
        }

        .navigation-links {
            margin-bottom: 20px;
            text-align: center;
        }

        .navigation-links a {
            color: white;
            text-decoration: none;
            margin: 0 10px;
            padding: 8px 15px;
            border-radius: 8px;
            background: rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .navigation-links a:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            text-decoration: none;
        }

        /* Footer Styles */
        .footer-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-top: 30px;
        }

        .footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links li {
            margin-bottom: 8px;
        }

        .footer-links a {
            color: #666;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #2c5530;
            text-decoration: none;
        }

        .footer-links h6 {
            color: #2c5530;
            font-weight: 600;
            margin-bottom: 15px;
            font-size: 1rem;
        }

        .footer-divider {
            border-color: #eee;
            margin: 25px 0 15px 0;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header-card">
            <h1><i class="fas fa-tachometer-alt me-3"></i>Admin Dashboard</h1>
            <p class="mb-0">Pakistan Identity Card Management System</p>
            <div class="navigation-links">
                <a href="show_data.php"><i class="fas fa-table me-1"></i>View All Records</a>
                <a href="index.php"><i class="fas fa-plus me-1"></i>Add Record</a>
                <a href="user_management.php"><i class="fas fa-users me-1"></i>Manage Users</a>
                <a href="logout.php"><i class="fas fa-sign-out-alt me-1"></i>Logout</a>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card primary">
                <h3 class="stat-number"><?php echo $stats['total_users']; ?></h3>
                <p class="stat-label"><i class="fas fa-users me-2"></i>Total Users</p>
            </div>
            
            <div class="stat-card success">
                <h3 class="stat-number"><?php echo $stats['total_records']; ?></h3>
                <p class="stat-label"><i class="fas fa-file-alt me-2"></i>Total Records</p>
            </div>
            
            <div class="stat-card warning">
                <h3 class="stat-number"><?php echo $stats['today_records']; ?></h3>
                <p class="stat-label"><i class="fas fa-calendar-day me-2"></i>Today's Records</p>
            </div>
            
            <div class="stat-card info">
                <h3 class="stat-number"><?php echo $stats['pending_records']; ?></h3>
                <p class="stat-label"><i class="fas fa-clock me-2"></i>Pending Records</p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="action-card">
            <h4 class="mb-3"><i class="fas fa-bolt me-2"></i>Quick Actions</h4>
            <div class="action-grid">
                <a href="index.php" class="action-btn">
                    <i class="fas fa-plus"></i>
                    Add New Record
                </a>
                <a href="show_data.php" class="action-btn">
                    <i class="fas fa-search"></i>
                    Search Records
                </a>
                <?php if (file_exists('user_management.php')): ?>
                <a href="user_management.php" class="action-btn">
                    <i class="fas fa-user-plus"></i>
                    Manage Users
                </a>
                <?php endif; ?>
                <a href="email_list.php" class="action-btn">
                    <i class="fas fa-envelope"></i>
                    Email Management
                </a>
            </div>
        </div>

        <div class="row">
            <!-- Recent Records -->
            <div class="col-lg-8">
                <div class="table-card">
                    <h4 class="mb-3"><i class="fas fa-history me-2"></i>Recent Records</h4>
                    <?php if ($recent_records && $recent_records->num_rows > 0): ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>CNIC</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($record = $recent_records->fetch_assoc()): ?>
                                    <tr>
                                        <td><strong><?php echo $record['id']; ?></strong></td>
                                        <td><?php echo htmlspecialchars($record['name'] ?? 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($record['cnic_number'] ?? 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($record['application_type'] ?? 'N/A'); ?></td>
                                        <td>
                                            <?php if (isset($record['status'])): ?>
                                                <span class="badge bg-<?php echo $record['status'] === 'Completed' ? 'success' : ($record['status'] === 'Pending' ? 'warning' : 'secondary'); ?>">
                                                    <?php echo $record['status']; ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Unknown</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="show_data.php" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-2"></i>View All Records
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-file-alt fa-3x mb-3"></i>
                            <h5>No Records Found</h5>
                            <p>No records available in the system.</p>
                            <a href="index.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add First Record
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- System Info -->
            <div class="col-lg-4">
                <div class="table-card">
                    <h4 class="mb-3"><i class="fas fa-info-circle me-2"></i>System Information</h4>
                    <div class="mb-3">
                        <strong>Logged in as:</strong><br>
                        <span class="text-muted"><?php echo htmlspecialchars($_SESSION['username']); ?></span>
                        <span class="badge bg-success ms-2">Admin</span>
                    </div>
                    <div class="mb-3">
                        <strong>Database Status:</strong><br>
                        <span class="text-success"><i class="fas fa-check-circle me-1"></i>Connected</span>
                    </div>
                    <div class="mb-3">
                        <strong>System Status:</strong><br>
                        <span class="text-success"><i class="fas fa-check-circle me-1"></i>Online</span>
                    </div>
                </div>

                <!-- Users List -->
                <div class="table-card mt-3">
                    <h4 class="mb-3"><i class="fas fa-users me-2"></i>Recent Users</h4>
                    <?php if ($users_list && $users_list->num_rows > 0): ?>
                        <div class="list-group list-group-flush">
                            <?php while ($user = $users_list->fetch_assoc()): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                    <?php if (isset($user['role'])): ?>
                                        <br><small class="text-muted">Role: <?php echo ucfirst($user['role']); ?></small>
                                    <?php endif; ?>
                                </div>
                                <?php if (isset($user['status'])): ?>
                                    <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                        <?php echo ucfirst($user['status']); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">No users found.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

        <!-- Admin Footer Navigation -->
        <footer class="mt-5">
            <div class="footer-card">
                <div class="row">
                    <div class="col-md-3">
                        <h6><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h6>
                        <ul class="footer-links">
                            <li><a href="admin_dashboard.php">Full Dashboard</a></li>
                            <li><a href="admin_dashboard_simple.php">Simple Dashboard</a></li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6><i class="fas fa-users me-2"></i>User Management</h6>
                        <ul class="footer-links">
                            <li><a href="user_management.php">Manage Users</a></li>
                            <li><a href="create_users_simple.php">Create Users</a></li>
                            <li><a href="change_password.php">Change Password</a></li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6><i class="fas fa-database me-2"></i>Records</h6>
                        <ul class="footer-links">
                            <li><a href="show_data.php">View All Records</a></li>
                            <li><a href="index.php">Add New Record</a></li>
                            <li><a href="email_list.php">Email Management</a></li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6><i class="fas fa-cogs me-2"></i>System</h6>
                        <ul class="footer-links">
                            <li><a href="debug_login.php">System Debug</a></li>
                            <li><a href="check_table_structure.php">Table Structure</a></li>
                            <li><a href="logout.php">Logout</a></li>
                        </ul>
                    </div>
                </div>
                <hr class="footer-divider">
                <div class="text-center">
                    <small class="text-muted">
                        &copy; <?php echo date('Y'); ?> Pakistan Identity Card Management System - Admin Panel
                    </small>
                </div>
            </div>
        </footer>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
