<?php
// Check Table Structure
require_once 'db_connection.php';

echo "<h2>🔍 Table Structure Check</h2>";

// Check tokenslip table structure
echo "<h3>Tokenslip Table Structure:</h3>";
$result = $conn->query("DESCRIBE tokenslip");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    $columns = [];
    while ($row = $result->fetch_assoc()) {
        $columns[] = $row['Field'];
        echo "<tr>";
        echo "<td><strong>" . $row['Field'] . "</strong></td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h4>Available Columns:</h4>";
    echo "<ul>";
    foreach ($columns as $col) {
        echo "<li><code>$col</code></li>";
    }
    echo "</ul>";
    
    // Check for date columns
    echo "<h4>Date/Time Columns Found:</h4>";
    $date_columns = [];
    foreach ($columns as $col) {
        if (stripos($col, 'date') !== false || stripos($col, 'time') !== false || stripos($col, 'created') !== false) {
            $date_columns[] = $col;
            echo "<li><strong style='color: green;'>$col</strong></li>";
        }
    }
    
    if (empty($date_columns)) {
        echo "<p style='color: red;'>❌ No date/time columns found!</p>";
        echo "<p><strong>Recommendation:</strong> Use a different column or add created_at column</p>";
    }
    
} else {
    echo "❌ Error: " . $conn->error;
}

// Check login table structure
echo "<h3>Login Table Structure:</h3>";
$result = $conn->query("DESCRIBE login");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td><strong>" . $row['Field'] . "</strong></td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ Error: " . $conn->error;
}

// Check if user_activity_logs table exists
echo "<h3>User Activity Logs Table:</h3>";
$result = $conn->query("SHOW TABLES LIKE 'user_activity_logs'");
if ($result->num_rows > 0) {
    echo "✅ Table exists<br>";
    $result = $conn->query("DESCRIBE user_activity_logs");
    if ($result) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td><strong>" . $row['Field'] . "</strong></td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "❌ Table does not exist";
}

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}
table {
    width: 100%;
    margin: 15px 0;
}
th, td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #ddd;
}
th {
    background: #f8f9fa;
}
code {
    background: #f1f1f1;
    padding: 2px 4px;
    border-radius: 3px;
}
</style>
