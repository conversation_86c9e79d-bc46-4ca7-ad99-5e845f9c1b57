<?php
// Simple User Creation Script
require_once 'db_connection.php';

echo "<h1>🔧 Simple User Creation Script</h1>";

// Step 1: Check and update login table structure
echo "<h2>Step 1: Preparing login table...</h2>";

// Add required columns if they don't exist
$columns_to_add = [
    "ALTER TABLE login ADD COLUMN IF NOT EXISTS role ENUM('admin', 'user') DEFAULT 'user'",
    "ALTER TABLE login ADD COLUMN IF NOT EXISTS full_name VARCHAR(100) DEFAULT NULL",
    "ALTER TABLE login ADD COLUMN IF NOT EXISTS status ENUM('active', 'inactive') DEFAULT 'active'"
];

foreach ($columns_to_add as $sql) {
    $conn->query($sql);
    echo "✅ Column added/checked<br>";
}

// Step 2: Delete existing users
echo "<h2>Step 2: Cleaning existing users...</h2>";
$conn->query("DELETE FROM login WHERE username IN ('admin', 'user1')");
echo "✅ Existing users removed<br>";

// Step 3: Create Admin User
echo "<h2>Step 3: Creating Admin User...</h2>";

$admin_username = 'admin';
$admin_password = 'admin123';
$admin_hash = password_hash($admin_password, PASSWORD_DEFAULT);

$sql = "INSERT INTO login (username, password, role, full_name, status) VALUES (?, ?, 'admin', 'Administrator', 'active')";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ss", $admin_username, $admin_hash);

if ($stmt->execute()) {
    $admin_id = $conn->insert_id;
    echo "✅ <strong>Admin user created successfully!</strong><br>";
    echo "ID: $admin_id<br>";
    echo "Username: <code>$admin_username</code><br>";
    echo "Password: <code>$admin_password</code><br>";
    echo "Role: admin<br>";
} else {
    echo "❌ Failed to create admin: " . $conn->error . "<br>";
}

// Step 4: Create Regular User
echo "<h2>Step 4: Creating Regular User...</h2>";

$user_username = 'user1';
$user_password = 'user123';
$user_hash = password_hash($user_password, PASSWORD_DEFAULT);

$sql = "INSERT INTO login (username, password, role, full_name, status) VALUES (?, ?, 'user', 'Regular User', 'active')";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ss", $user_username, $user_hash);

if ($stmt->execute()) {
    $user_id = $conn->insert_id;
    echo "✅ <strong>Regular user created successfully!</strong><br>";
    echo "ID: $user_id<br>";
    echo "Username: <code>$user_username</code><br>";
    echo "Password: <code>$user_password</code><br>";
    echo "Role: user<br>";
} else {
    echo "❌ Failed to create user: " . $conn->error . "<br>";
}

// Step 5: Verify users
echo "<h2>Step 5: Verification...</h2>";

$result = $conn->query("SELECT id, username, role, status FROM login WHERE username IN ('admin', 'user1')");
if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Username</th><th>Role</th><th>Status</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td><strong>" . $row['username'] . "</strong></td>";
        echo "<td>" . $row['role'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ No users found!<br>";
}

// Step 6: Test passwords
echo "<h2>Step 6: Password Test...</h2>";

// Test admin
$result = $conn->query("SELECT password FROM login WHERE username = 'admin'");
if ($result && $row = $result->fetch_assoc()) {
    if (password_verify('admin123', $row['password'])) {
        echo "✅ Admin password test: <strong style='color: green;'>PASSED</strong><br>";
    } else {
        echo "❌ Admin password test: <strong style='color: red;'>FAILED</strong><br>";
    }
}

// Test user
$result = $conn->query("SELECT password FROM login WHERE username = 'user1'");
if ($result && $row = $result->fetch_assoc()) {
    if (password_verify('user123', $row['password'])) {
        echo "✅ User password test: <strong style='color: green;'>PASSED</strong><br>";
    } else {
        echo "❌ User password test: <strong style='color: red;'>FAILED</strong><br>";
    }
}

// Success message
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2 style='color: #155724; margin-top: 0;'>🎉 Users Created Successfully!</h2>";
echo "<h3>Login Credentials:</h3>";
echo "<p><strong>Admin Account:</strong><br>";
echo "Username: <code style='background: white; padding: 3px 6px; border-radius: 3px;'>admin</code><br>";
echo "Password: <code style='background: white; padding: 3px 6px; border-radius: 3px;'>admin123</code><br>";
echo "Access: Full system access</p>";

echo "<p><strong>User Account:</strong><br>";
echo "Username: <code style='background: white; padding: 3px 6px; border-radius: 3px;'>user1</code><br>";
echo "Password: <code style='background: white; padding: 3px 6px; border-radius: 3px;'>user123</code><br>";
echo "Access: Limited to own records</p>";
echo "</div>";

// Login links
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3 style='color: #856404; margin-top: 0;'>🔗 Login Pages:</h3>";
echo "<p><a href='login_multiuser.php' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px;'>New Multi-User Login</a></p>";
echo "<p><a href='login.php' style='background: #6c757d; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px;'>Original Login</a></p>";
echo "</div>";

// Quick test form
echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🧪 Quick Login Test:</h3>";
echo "<form method='POST' action=''>";
echo "<p>";
echo "<label>Username:</label><br>";
echo "<select name='test_username' style='padding: 8px; margin: 5px; width: 200px;'>";
echo "<option value='admin'>admin</option>";
echo "<option value='user1'>user1</option>";
echo "</select>";
echo "</p>";
echo "<p>";
echo "<label>Password:</label><br>";
echo "<input type='password' name='test_password' placeholder='Enter password' style='padding: 8px; margin: 5px; width: 200px;'>";
echo "</p>";
echo "<button type='submit' name='test_login' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Test Login</button>";
echo "</form>";

if (isset($_POST['test_login'])) {
    $test_user = $_POST['test_username'];
    $test_pass = $_POST['test_password'];
    
    echo "<h4>Test Result:</h4>";
    
    $stmt = $conn->prepare("SELECT id, username, password, role, status FROM login WHERE username = ?");
    $stmt->bind_param("s", $test_user);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        
        if ($user['status'] !== 'active') {
            echo "<p style='color: red;'>❌ Account not active</p>";
        } elseif (password_verify($test_pass, $user['password'])) {
            echo "<p style='color: green; font-weight: bold;'>✅ LOGIN SUCCESSFUL!</p>";
            echo "<p>User: " . $user['username'] . " (Role: " . $user['role'] . ")</p>";
            echo "<p><strong>✅ You can now use these credentials to login!</strong></p>";
        } else {
            echo "<p style='color: red;'>❌ Wrong password</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ User not found</p>";
    }
}
echo "</div>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}
h1, h2, h3 {
    color: #333;
}
code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}
table {
    width: 100%;
    margin: 15px 0;
    background: white;
}
th, td {
    padding: 10px;
    text-align: left;
    border: 1px solid #ddd;
}
th {
    background: #f8f9fa;
    font-weight: bold;
}
</style>
