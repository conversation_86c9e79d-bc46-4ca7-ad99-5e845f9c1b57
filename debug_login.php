<?php
// Login Debug Script
require_once 'db_connection.php';

echo "<h2>Login System Debug</h2>";

// Test database connection
echo "<h3>1. Database Connection Test:</h3>";
if ($conn) {
    echo "✓ Database connected successfully<br>";
} else {
    echo "✗ Database connection failed<br>";
    exit();
}

// Check if login table exists and has required columns
echo "<h3>2. Login Table Structure:</h3>";
$result = $conn->query("DESCRIBE login");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "✗ Login table not found or error: " . $conn->error;
}

// Check users in database
echo "<h3>3. Users in Database:</h3>";
$result = $conn->query("SELECT id, username, role, status, full_name FROM login");
if ($result) {
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Role</th><th>Status</th><th>Full Name</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['username'] . "</td>";
            echo "<td>" . ($row['role'] ?? 'NULL') . "</td>";
            echo "<td>" . ($row['status'] ?? 'NULL') . "</td>";
            echo "<td>" . ($row['full_name'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "⚠ No users found in database";
    }
} else {
    echo "✗ Error fetching users: " . $conn->error;
}

// Test password hashing
echo "<h3>4. Password Hash Test:</h3>";
$test_password = 'admin123';
$hash = password_hash($test_password, PASSWORD_DEFAULT);
echo "Original password: <code>$test_password</code><br>";
echo "Generated hash: <code>$hash</code><br>";
echo "Verification test: " . (password_verify($test_password, $hash) ? "✓ SUCCESS" : "✗ FAILED") . "<br>";

// Test actual user passwords
echo "<h3>5. Actual User Password Test:</h3>";
$users_to_test = [
    ['username' => 'admin', 'password' => 'admin123'],
    ['username' => 'user1', 'password' => 'user123']
];

foreach ($users_to_test as $test_user) {
    $stmt = $conn->prepare("SELECT id, username, password FROM login WHERE username = ?");
    $stmt->bind_param("s", $test_user['username']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $verification = password_verify($test_user['password'], $user['password']);
        echo "User: <strong>" . $user['username'] . "</strong><br>";
        echo "Password hash: <code>" . substr($user['password'], 0, 30) . "...</code><br>";
        echo "Verification with '" . $test_user['password'] . "': " . ($verification ? "✓ SUCCESS" : "✗ FAILED") . "<br><br>";
    } else {
        echo "User <strong>" . $test_user['username'] . "</strong>: ✗ NOT FOUND<br><br>";
    }
}

// Manual login test form
echo "<h3>6. Manual Login Test:</h3>";
?>

<form method="POST" action="" style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
    <label>Username:</label><br>
    <input type="text" name="test_username" value="admin" style="margin: 5px; padding: 5px; width: 200px;"><br>
    
    <label>Password:</label><br>
    <input type="password" name="test_password" value="admin123" style="margin: 5px; padding: 5px; width: 200px;"><br>
    
    <button type="submit" name="manual_test" style="margin: 5px; padding: 8px 15px; background: #007bff; color: white; border: none; border-radius: 3px;">Test Login</button>
</form>

<?php
if (isset($_POST['manual_test'])) {
    $username = trim($_POST['test_username']);
    $password = $_POST['test_password'];
    
    echo "<h4>Manual Test Result:</h4>";
    
    if (empty($username) || empty($password)) {
        echo "✗ Username or password is empty<br>";
    } else {
        $stmt = $conn->prepare("SELECT id, username, password, role, full_name, status FROM login WHERE username = ?");
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();
            echo "✓ User found in database<br>";
            echo "User ID: " . $user['id'] . "<br>";
            echo "Username: " . $user['username'] . "<br>";
            echo "Role: " . ($user['role'] ?? 'NULL') . "<br>";
            echo "Status: " . ($user['status'] ?? 'NULL') . "<br>";
            echo "Full Name: " . ($user['full_name'] ?? 'NULL') . "<br>";
            
            if ($user['status'] !== 'active') {
                echo "⚠ Account status is not 'active': " . $user['status'] . "<br>";
            }
            
            if (password_verify($password, $user['password'])) {
                echo "✓ Password verification: <strong style='color: green;'>SUCCESS</strong><br>";
                echo "🎉 Login should work!<br>";
            } else {
                echo "✗ Password verification: <strong style='color: red;'>FAILED</strong><br>";
                echo "Password hash in DB: " . substr($user['password'], 0, 30) . "...<br>";
            }
        } else {
            echo "✗ User '$username' not found in database<br>";
        }
    }
}

echo "<h3>7. Quick Actions:</h3>";
echo "<a href='fix_passwords.php' style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; margin: 5px;'>Fix Passwords</a>";
echo "<a href='login_multiuser.php' style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; margin: 5px;'>Try Login</a>";
echo "<a href='setup_multiuser_fixed.php' style='background: #ffc107; color: black; padding: 8px 15px; text-decoration: none; border-radius: 3px; margin: 5px;'>Re-run Setup</a>";

$conn->close();
?>
