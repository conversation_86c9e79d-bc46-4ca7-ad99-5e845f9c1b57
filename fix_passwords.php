<?php
// Password Fix Script
require_once 'db_connection.php';

echo "<h2>Fixing User Passwords...</h2>";

// Check current users
echo "<h3>Current users in database:</h3>";
$result = $conn->query("SELECT id, username, password FROM login");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        echo "ID: " . $row['id'] . " | Username: " . $row['username'] . " | Password Hash: " . substr($row['password'], 0, 20) . "...<br>";
    }
}

echo "<br><h3>Updating passwords with correct hashing...</h3>";

// Fix admin password
$admin_password = password_hash('admin123', PASSWORD_DEFAULT);
$stmt = $conn->prepare("UPDATE login SET password = ? WHERE username = 'admin'");
$stmt->bind_param("s", $admin_password);
if ($stmt->execute()) {
    echo "✓ Admin password updated successfully<br>";
    echo "New hash: " . substr($admin_password, 0, 30) . "...<br>";
} else {
    echo "✗ Failed to update admin password: " . $conn->error . "<br>";
}

// Fix user1 password
$user_password = password_hash('user123', PASSWORD_DEFAULT);
$stmt = $conn->prepare("UPDATE login SET password = ? WHERE username = 'user1'");
$stmt->bind_param("s", $user_password);
if ($stmt->execute()) {
    echo "✓ User1 password updated successfully<br>";
    echo "New hash: " . substr($user_password, 0, 30) . "...<br>";
} else {
    echo "✗ Failed to update user1 password: " . $conn->error . "<br>";
}

// Test password verification
echo "<br><h3>Testing password verification:</h3>";

// Test admin password
$result = $conn->query("SELECT password FROM login WHERE username = 'admin'");
if ($result && $row = $result->fetch_assoc()) {
    if (password_verify('admin123', $row['password'])) {
        echo "✓ Admin password verification: SUCCESS<br>";
    } else {
        echo "✗ Admin password verification: FAILED<br>";
    }
}

// Test user password
$result = $conn->query("SELECT password FROM login WHERE username = 'user1'");
if ($result && $row = $result->fetch_assoc()) {
    if (password_verify('user123', $row['password'])) {
        echo "✓ User1 password verification: SUCCESS<br>";
    } else {
        echo "✗ User1 password verification: FAILED<br>";
    }
}

echo "<br><h3>🎉 Password Fix Complete!</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>Updated Login Credentials:</h4>";
echo "<p><strong>Admin Login:</strong><br>";
echo "Username: <code>admin</code><br>";
echo "Password: <code>admin123</code></p>";

echo "<p><strong>User Login:</strong><br>";
echo "Username: <code>user1</code><br>";
echo "Password: <code>user123</code></p>";

echo "<p><strong>Login URL:</strong><br>";
echo "<a href='login_multiuser.php'>login_multiuser.php</a></p>";
echo "</div>";

$conn->close();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Password Fix Complete</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="test-section">
        <h4>🧪 Quick Test:</h4>
        <p>Ab ye test karo:</p>
        <ol>
            <li><a href="login_multiuser.php" target="_blank">Login page kholo</a></li>
            <li>Admin: <code>admin</code> / <code>admin123</code></li>
            <li>User: <code>user1</code> / <code>user123</code></li>
        </ol>
    </div>
    
    <div class="test-section">
        <h4>🔧 Manual Password Test:</h4>
        <form method="POST" action="">
            <label>Test Username:</label>
            <input type="text" name="test_username" placeholder="admin or user1" style="margin: 5px;">
            <br>
            <label>Test Password:</label>
            <input type="password" name="test_password" placeholder="admin123 or user123" style="margin: 5px;">
            <br>
            <button type="submit" name="test_login" style="margin: 5px; padding: 5px 10px;">Test Login</button>
        </form>
        
        <?php
        if (isset($_POST['test_login'])) {
            $test_username = $_POST['test_username'];
            $test_password = $_POST['test_password'];
            
            $conn = new mysqli($servername, $username, $password, $dbname);
            $stmt = $conn->prepare("SELECT password FROM login WHERE username = ?");
            $stmt->bind_param("s", $test_username);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $row = $result->fetch_assoc();
                if (password_verify($test_password, $row['password'])) {
                    echo "<p class='success'>✓ Login test SUCCESSFUL for $test_username</p>";
                } else {
                    echo "<p class='error'>✗ Login test FAILED for $test_username</p>";
                }
            } else {
                echo "<p class='error'>✗ User $test_username not found</p>";
            }
            $conn->close();
        }
        ?>
    </div>
</body>
</html>
