<?php
session_start();
require_once 'db_connection.php';

$error_message = '';
$success_message = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['login'])) {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    
    if (empty($username) || empty($password)) {
        $error_message = 'Please enter both username and password.';
    } else {
        // Check user credentials
        $stmt = $conn->prepare("SELECT id, username, password, role, full_name, status FROM login WHERE username = ?");
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $user = $result->fetch_assoc();
            
            // Check if status column exists and user is active
            if (isset($user['status']) && $user['status'] !== 'active') {
                $error_message = 'Your account has been deactivated.';
            } elseif (password_verify($password, $user['password'])) {
                // Login successful
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = isset($user['role']) ? $user['role'] : 'user';
                $_SESSION['full_name'] = isset($user['full_name']) ? $user['full_name'] : $user['username'];
                $_SESSION['last_activity'] = time();
                
                // Update last login time if column exists
                try {
                    $stmt = $conn->prepare("UPDATE login SET last_login = NOW() WHERE id = ?");
                    $stmt->bind_param("i", $user['id']);
                    $stmt->execute();
                } catch (Exception $e) {
                    // Ignore if last_login column doesn't exist
                }
                
                // Redirect based on role
                if (isset($user['role']) && $user['role'] === 'admin') {
                    // Check if admin dashboard exists
                    if (file_exists('admin_dashboard.php')) {
                        header("Location: admin_dashboard.php");
                    } else {
                        header("Location: show_data.php"); // Fallback to existing page
                    }
                } else {
                    // Check if user dashboard exists
                    if (file_exists('user_dashboard.php')) {
                        header("Location: user_dashboard.php");
                    } elseif (file_exists('show_data.php')) {
                        header("Location: show_data.php"); // Fallback to existing page
                    } else {
                        header("Location: index.php"); // Final fallback
                    }
                }
                exit();
            } else {
                $error_message = 'Invalid username or password.';
            }
        } else {
            $error_message = 'Invalid username or password.';
        }
    }
}

// Handle success messages
if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 'logout':
            $success_message = 'You have been logged out successfully.';
            break;
        case 'password_changed':
            $success_message = 'Password changed successfully.';
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Pakistan Identity System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-card {
            max-width: 400px;
            width: 100%;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .login-header {
            background: linear-gradient(135deg, #2c5530 0%, #4a7c59 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .login-header h2 {
            margin: 0;
            font-weight: 600;
        }

        .login-body {
            padding: 30px;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #2c5530;
            box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
        }

        .input-group-text {
            background: #2c5530;
            color: white;
            border: none;
            border-radius: 10px 0 0 10px;
        }

        .input-group .form-control {
            border-left: none;
            border-radius: 0 10px 10px 0;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2c5530 0%, #4a7c59 100%);
            border: none;
            padding: 12px;
            font-weight: 600;
            border-radius: 10px;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(44, 85, 48, 0.3);
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 15px;
            margin-bottom: 20px;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .login-footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .test-credentials {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }

        .test-credentials h6 {
            color: #856404;
            margin-bottom: 10px;
        }

        .test-credentials small {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="login-card">
        <!-- Header -->
        <div class="login-header">
            <h2><i class="fas fa-id-card me-2"></i>Pakistan Identity System</h2>
            <p class="mb-0">Multi-User Login</p>
        </div>

        <!-- Body -->
        <div class="login-body">
            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" class="form-control" id="username" name="username" 
                               placeholder="Enter username" required 
                               value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                    </div>
                </div>

                <div class="mb-4">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="Enter password" required>
                    </div>
                </div>

                <button type="submit" name="login" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>Login
                </button>
            </form>

            <!-- Test Credentials -->
            <div class="test-credentials">
                <h6><i class="fas fa-info-circle me-2"></i>Test Credentials</h6>
                <small>
                    <strong>Admin:</strong> admin / admin123<br>
                    <strong>User:</strong> user1 / user123
                </small>
            </div>
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <small>&copy; <?php echo date('Y'); ?> Pakistan Identity Card System</small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Focus on username field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });

        // Add loading state to login button
        document.querySelector('form').addEventListener('submit', function() {
            const btn = document.querySelector('button[type="submit"]');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Logging in...';
            btn.disabled = true;
            
            // Re-enable button after 5 seconds in case of error
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 5000);
        });

        // Quick fill buttons for testing
        function fillAdmin() {
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'admin123';
        }

        function fillUser() {
            document.getElementById('username').value = 'user1';
            document.getElementById('password').value = 'user123';
        }

        // Add quick fill buttons
        document.addEventListener('DOMContentLoaded', function() {
            const testDiv = document.querySelector('.test-credentials');
            testDiv.innerHTML += '<br><button type="button" class="btn btn-sm btn-outline-warning me-2" onclick="fillAdmin()">Fill Admin</button>';
            testDiv.innerHTML += '<button type="button" class="btn btn-sm btn-outline-info" onclick="fillUser()">Fill User</button>';
        });
    </script>
</body>
</html>
