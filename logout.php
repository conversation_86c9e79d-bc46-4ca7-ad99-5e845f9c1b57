<?php
session_start();

// Include database connection
include 'db_connection.php';

// Log logout activity if user is logged in
if (isset($_SESSION['user_id'])) {
    try {
        $ip_address = $_SERVER['REMOTE_ADDR'];
        $user_agent = $_SERVER['HTTP_USER_AGENT'];

        // Check if user_activity_logs table exists
        $result = $conn->query("SHOW TABLES LIKE 'user_activity_logs'");
        if ($result->num_rows > 0) {
            // Log the logout activity
            $stmt = $conn->prepare("INSERT INTO user_activity_logs (user_id, action, ip_address, user_agent) VALUES (?, 'logout', ?, ?)");
            $stmt->bind_param("iss", $_SESSION['user_id'], $ip_address, $user_agent);
            $stmt->execute();
            $stmt->close();
        }
    } catch (Exception $e) {
        // Silently continue if there's an error with logging
        error_log("Logout logging failed: " . $e->getMessage());
    }
}

// Clear all session variables
$_SESSION = array();

// Destroy the session cookie
if (isset($_COOKIE[session_name()])) {
    setcookie(session_name(), '', time()-3600, '/');
}

// Destroy the session
session_destroy();

// Close database connection
$conn->close();

// Redirect to login page with success message
header("Location: login_multiuser.php?success=logout");
exit();
?>
