<?php
include('db_connection.php');
include('session.php');

if (!isset($_GET['id'])) {
    die("ID parameter is missing");
}

$id = $_GET['id'];
$stmt = $conn->prepare("SELECT * FROM tokenslip WHERE id = ?");
$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    die("Record not found");
}

$row = $result->fetch_assoc();
?>
<!DOCTYPE html>
<html lang="ur" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بیان حلفی - Affidavit</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts for Urdu -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Nastaliq+Urdu:wght@400;500;600;700&display=swap" rel="stylesheet">

    <?php
    function getImageUrl($imagePath) {
        // Check if image exists in uploads directory
        $localPath = __DIR__ . '/uploads/' . $imagePath;
        if (file_exists($localPath)) {
            // Get the directory name of the current script
            $dirName = dirname($_SERVER['PHP_SELF']);
            // If not in root directory, add the directory path
            $baseUrl = $dirName !== '/' ? $dirName : '';
            return $baseUrl . '/uploads/' . $imagePath;
        }
        return ''; // Return empty if image not found
    }
    ?>
    <style>
        :root {
            --primary-color: #2c5530;
            --secondary-color: #4a7c59;
            --accent-color: #f8f9fa;
            --text-dark: #2d3436;
            --border-color: #e9ecef;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --gold-color: #ffd700;
        }

        @font-face {
            font-family: 'Jameel Noori Nastaleeq';
            src: url('fonts/Jameel-Noori-Nastaliq.ttf') format('truetype');
        }

        body {
            font-family: 'Noto Nastaliq Urdu', 'Jameel Noori Nastaleeq', Arial, sans-serif;
            line-height: 1.4;
            direction: rtl;
            padding: 10px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .main-container {
            max-width: 210mm; /* A4 width */
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
            max-height: 297mm; /* A4 height */
        }

        .document-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 15px 15px;
            text-align: center;
            position: relative;
        }

        .document-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .document-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
            position: relative;
            z-index: 1;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .document-header .subtitle {
            font-size: 0.85rem;
            opacity: 0.9;
            margin-top: 5px;
            position: relative;
            z-index: 1;
            font-weight: 400;
        }

        .document-content {
            padding: 15px;
        }

        .urdu-text {
            font-size: 1rem;
            text-align: right;
            line-height: 1.4;
            color: var(--text-dark);
        }

        .number-text {
            direction: ltr;
            display: inline-block;
            text-align: left;
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            color: var(--primary-color);
        }

        .applicant-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 12px;
            border-radius: 10px;
            margin-bottom: 10px;
            border-left: 3px solid var(--primary-color);
            box-shadow: 0 3px 8px rgba(0,0,0,0.05);
            position: relative;
        }

        .photo-container {
            float: left;
            margin-left: 10px;
            margin-bottom: 10px;
        }

        .photo-box {
            width: 100px;
            height: 120px;
            border: 2px solid var(--primary-color);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            box-shadow: 0 3px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
        }

        .photo-box::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--gold-color));
            border-radius: 15px;
            z-index: -1;
        }

        .photo-box img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 12px;
        }

        .photo-placeholder {
            color: var(--primary-color);
            font-size: 1.5rem;
            text-align: center;
            font-weight: 600;
        }

        .info-section {
            background: white;
            padding: 8px;
            border-radius: 8px;
            margin-bottom: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .info-item {
            margin-bottom: 5px;
            padding: 3px 0;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.9rem;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 700;
            color: var(--primary-color);
            margin-left: 5px;
        }

        .statement-section {
            background: white;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .statement-item {
            margin-bottom: 6px;
            padding: 6px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 5px;
            border-right: 2px solid var(--primary-color);
            font-size: 0.85rem;
            line-height: 1.3;
        }

        .important-note {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            font-weight: 600;
            margin: 8px 0;
            padding: 8px;
            border: 1px solid var(--warning-color);
            border-radius: 6px;
            box-shadow: 0 2px 5px rgba(255, 193, 7, 0.2);
            position: relative;
            font-size: 0.8rem;
            line-height: 1.3;
        }

        .important-note::before {
            content: '\f071';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            top: 8px;
            right: 10px;
            font-size: 1rem;
            color: var(--warning-color);
        }

        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            gap: 10px;
        }

        .signature-box {
            background: white;
            padding: 8px;
            border-radius: 6px;
            text-align: center;
            flex: 1;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            border: 1px solid var(--border-color);
        }

        .signature-line {
            border-top: 1px solid var(--primary-color);
            margin-top: 25px;
            padding-top: 5px;
            font-weight: 600;
            color: var(--primary-color);
            font-size: 0.8rem;
        }

        .footer-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 8px;
            border-radius: 6px;
            margin-top: 8px;
            text-align: center;
            border-top: 1px solid var(--primary-color);
            font-size: 0.75rem;
        }

        .print-buttons {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
        }

        .btn-modern {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 25px;
            transition: all 0.3s ease;
            margin: 0 10px;
            box-shadow: 0 5px 15px rgba(44, 85, 48, 0.3);
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(44, 85, 48, 0.4);
            color: white;
        }

        .btn-secondary-modern {
            background: #6c757d;
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
        }

        .btn-secondary-modern:hover {
            background: #5a6268;
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
        }

        /* A4 Single Page Print Styles */
        @media print {
            @page {
                size: A4;
                margin: 10mm 8mm 10mm 8mm;
            }

            .no-print {
                display: none !important;
            }

            body {
                background: white !important;
                padding: 0 !important;
                margin: 0 !important;
                font-size: 9pt !important;
                line-height: 1.2 !important;
            }

            .main-container {
                box-shadow: none !important;
                border-radius: 0 !important;
                margin: 0 !important;
                max-width: none !important;
                width: 100% !important;
                height: auto !important;
                max-height: 277mm !important; /* A4 height minus margins */
                overflow: hidden !important;
            }

            .document-header {
                background: var(--primary-color) !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                padding: 8mm 5mm !important;
                margin-bottom: 2mm !important;
            }

            .document-header h1 {
                font-size: 16pt !important;
                margin: 0 !important;
            }

            .document-header .subtitle {
                font-size: 8pt !important;
                margin-top: 2mm !important;
            }

            .document-content {
                padding: 2mm 5mm !important;
            }

            .applicant-info {
                padding: 3mm !important;
                margin-bottom: 2mm !important;
                page-break-inside: avoid;
            }

            .photo-box {
                width: 25mm !important;
                height: 30mm !important;
                border: 1pt solid #000 !important;
                border-radius: 2mm !important;
                margin-left: 3mm !important;
            }

            .info-section {
                padding: 2mm !important;
                margin-bottom: 1mm !important;
            }

            .info-item {
                margin-bottom: 1mm !important;
                padding: 0.5mm 0 !important;
                font-size: 8pt !important;
            }

            .statement-section {
                padding: 2mm !important;
                margin-bottom: 1mm !important;
            }

            .statement-item {
                margin-bottom: 1mm !important;
                padding: 1.5mm !important;
                font-size: 8pt !important;
                line-height: 1.2 !important;
            }

            .important-note {
                margin: 1mm 0 !important;
                padding: 2mm !important;
                font-size: 7pt !important;
                border: 0.5pt solid #000 !important;
            }

            .signature-section {
                margin-top: 3mm !important;
                gap: 5mm !important;
            }

            .signature-box {
                padding: 2mm !important;
                border: 0.5pt solid #000 !important;
                font-size: 7pt !important;
            }

            .signature-line {
                border-top: 1pt solid #000 !important;
                margin-top: 8mm !important;
                padding-top: 1mm !important;
                font-size: 7pt !important;
            }

            .footer-section {
                padding: 2mm !important;
                margin-top: 2mm !important;
                border-top: 0.5pt solid #000 !important;
                font-size: 6pt !important;
            }

            .urdu-text {
                font-size: 8pt !important;
                line-height: 1.2 !important;
            }

            .number-text {
                font-size: 8pt !important;
                font-weight: bold !important;
            }

            .info-label {
                font-weight: bold !important;
            }

            /* Force single page */
            .statement-section .urdu-text {
                font-size: 7pt !important;
                margin-bottom: 1mm !important;
            }

            .photo-container {
                margin-left: 2mm !important;
                margin-bottom: 1mm !important;
            }

            /* Ensure no page breaks */
            * {
                page-break-inside: avoid !important;
            }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .document-header {
                padding: 30px 20px;
            }

            .document-header h1 {
                font-size: 2.5rem;
            }

            .document-content {
                padding: 25px;
            }

            .photo-container {
                float: none;
                margin: 0 auto 20px;
                text-align: center;
            }

            .signature-section {
                flex-direction: column;
                gap: 20px;
            }

            .urdu-text {
                font-size: 1.1rem;
            }
        }

        @media (max-width: 480px) {
            .document-header h1 {
                font-size: 2rem;
            }

            .document-content {
                padding: 20px;
            }

            .photo-box {
                width: 150px;
                height: 180px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Document Header -->
        <div class="document-header">
            <h1>بیان حلفی</h1>
            <p class="subtitle">Affidavit - Pakistan Identity Card Application</p>
        </div>

        <!-- Document Content -->
        <div class="document-content">
            <!-- Applicant Information -->
            <div class="applicant-info">
                <div class="photo-container">
                    <div class="photo-box">
                        <?php if (!empty($row['image_path'])): ?>
                            <img src="get_image.php?filename=<?php echo urlencode($row['image_path']); ?>" alt="Applicant Photo">
                        <?php else: ?>
                            <div class="photo-placeholder">
                                <i class="fas fa-user fa-3x mb-2"></i>
                                <div>تصویر</div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="info-section urdu-text">
                    <div class="info-item">
                        <span class="info-label">نام:</span>
                        <?php echo htmlspecialchars($row['name']); ?>
                    </div>
                    <div class="info-item">
                        <span class="info-label">ولد:</span>
                        <?php echo htmlspecialchars($row['father_name']); ?>
                    </div>
                    <div class="info-item">
                        <span class="info-label">شناختی کارڈ نمبر:</span>
                        <span class="number-text"><?php echo htmlspecialchars($row['cnic_number']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">موبائل نمبر:</span>
                        <span class="number-text"><?php echo htmlspecialchars($row['mobile_number']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">پتہ:</span>
                        <?php echo htmlspecialchars($row['present_address']); ?>
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

            <!-- Statement Section -->
            <div class="statement-section">
                <div class="urdu-text" style="text-align: center; margin-bottom: 8px; font-size: 1.1rem; font-weight: 700; color: var(--primary-color);">
                    میں حلف اٹھا کر بیان کرتا/کرتی ہوں:
                </div>

                <div class="statement-item urdu-text">
                    <strong>1.</strong> یہ کہ میں بالغ و عاقل ہوں اور اپنے حواس و قوائے دماغی کے پورے ہوش و حواس میں ہوں۔
                </div>

                <div class="statement-item urdu-text">
                    <strong>2.</strong> یہ کہ میں نے اپنی شناختی دستاویزات کے لیے درخواست دی ہے۔
                </div>

                <div class="statement-item urdu-text">
                    <strong>3.</strong> یہ کہ میری تمام معلومات درست ہیں اور میں نے کوئی غلط معلومات نہیں دی ہیں۔
                </div>

                <div class="statement-item urdu-text">
                    <strong>4.</strong> یہ کہ اگر میری دی گئی معلومات غلط ثابت ہوتی ہیں تو میں قانونی کارروائی کا/کی ذمہ دار ہوں گا/گی۔
                </div>

                <div class="important-note urdu-text">
                    <strong>5.</strong> یہ کہ درخواست کے ریجیکٹ ہونے کی صورت میں میری سروس فیس ناقابل واپسی ہے، صرف میری درخواست فیس واپس ہوگی وہ بھی نادرا اگر واپس کرے گا تو، نہیں تو جو نادرا کا فیصلہ ہوگا وہ قبول کرنا ہوگا۔
                </div>
            </div>

            <!-- Signature Section -->
            <div class="signature-section">
                <div class="signature-box urdu-text">
                    <div style="margin-bottom: 15px;">
                        <i class="fas fa-signature" style="color: var(--primary-color); font-size: 1.5rem; margin-bottom: 10px;"></i>
                    </div>
                    <div class="signature-line">دستخط درخواست گزار</div>
                    <div style="margin-top: 15px; font-size: 0.9rem;">
                        <strong>نام:</strong> <?php echo htmlspecialchars($row['name']); ?>
                    </div>
                    <div style="font-size: 0.9rem;">
                        <strong>شناختی کارڈ نمبر:</strong><br>
                        <span class="number-text"><?php echo htmlspecialchars($row['cnic_number']); ?></span>
                    </div>
                </div>

                <div class="signature-box urdu-text">
                    <div style="margin-bottom: 15px;">
                        <i class="fas fa-fingerprint" style="color: var(--primary-color); font-size: 1.5rem; margin-bottom: 10px;"></i>
                    </div>
                    <div class="signature-line">انگوٹھے کا نشان</div>
                    <div style="margin-top: 15px; font-size: 0.9rem; color: #666;">
                        Thumb Impression
                    </div>
                </div>
            </div>

            <!-- Footer Section -->
            <div class="footer-section urdu-text">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <i class="fas fa-calendar-alt" style="color: var(--primary-color); margin-left: 10px;"></i>
                        <strong>تاریخ:</strong> <span class="number-text"><?php echo date('d-m-Y'); ?></span>
                    </div>
                    <div>
                        <i class="fas fa-clock" style="color: var(--primary-color); margin-left: 10px;"></i>
                        <strong>وقت:</strong> <span class="number-text"><?php echo date('h:i A'); ?></span>
                    </div>
                </div>
                <div style="margin-top: 15px; text-align: center; font-size: 0.9rem; color: #666;">
                    Generated by Pakistan Identity Card Management System
                </div>
            </div>
        </div>

        <!-- Print Buttons -->
        <div class="print-buttons no-print">
            <button onclick="window.print()" class="btn-modern">
                <i class="fas fa-print me-2"></i>Print Document
            </button>
            <button onclick="window.close()" class="btn-modern btn-secondary-modern">
                <i class="fas fa-times me-2"></i>Close Window
            </button>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto-focus for better user experience
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth animations
            const elements = document.querySelectorAll('.statement-item, .signature-box, .info-section');
            elements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    el.style.transition = 'all 0.6s ease';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // Enhanced print function
        function printDocument() {
            window.print();
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                printDocument();
            }
            if (e.key === 'Escape') {
                window.close();
            }
        });
    </script>
</body>
</html>
<?php
$stmt->close();
$conn->close();
?>
