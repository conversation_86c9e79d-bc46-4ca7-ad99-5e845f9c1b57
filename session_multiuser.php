<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database connection for user verification
require_once 'db_connection.php';

// Session timeout duration in seconds (30 minutes)
$session_timeout = 1800;

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Verify user still exists and is active
$stmt = $conn->prepare("SELECT id, username, role, full_name, status FROM login WHERE id = ? AND status = 'active'");
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    // User doesn't exist or is inactive
    session_unset();
    session_destroy();
    header("Location: login.php?error=account_disabled");
    exit();
}

$user_data = $result->fetch_assoc();
$_SESSION['username'] = $user_data['username'];
$_SESSION['role'] = $user_data['role'];
$_SESSION['full_name'] = $user_data['full_name'];

// Check for session timeout
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > $session_timeout)) {
    // Session has expired
    session_unset();
    session_destroy();
    header("Location: login.php?timeout=1");
    exit();
}

// Update last activity time stamp
$_SESSION['last_activity'] = time();

// Regenerate session ID periodically to prevent session fixation
if (!isset($_SESSION['created'])) {
    $_SESSION['created'] = time();
} else if (time() - $_SESSION['created'] > 1800) {
    // Regenerate session ID every 30 minutes
    session_regenerate_id(true);
    $_SESSION['created'] = time();
}

// Verify user agent to prevent session hijacking
if (!isset($_SESSION['user_agent'])) {
    $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'];
} else if ($_SESSION['user_agent'] !== $_SERVER['HTTP_USER_AGENT']) {
    session_unset();
    session_destroy();
    header("Location: login.php?error=security");
    exit();
}

// Function to check if user has permission
function hasPermission($permission) {
    global $conn;
    
    // Admin has all permissions
    if ($_SESSION['role'] === 'admin') {
        return true;
    }
    
    // Check specific permission
    $stmt = $conn->prepare("SELECT id FROM user_permissions WHERE user_id = ? AND permission = ?");
    $stmt->bind_param("is", $_SESSION['user_id'], $permission);
    $stmt->execute();
    $result = $stmt->get_result();
    
    return $result->num_rows > 0;
}

// Function to check if user is admin
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

// Function to log user activity
function logActivity($action, $table_name = null, $record_id = null, $old_values = null, $new_values = null) {
    global $conn;
    
    $ip_address = $_SERVER['REMOTE_ADDR'];
    $user_agent = $_SERVER['HTTP_USER_AGENT'];
    
    $old_json = $old_values ? json_encode($old_values) : null;
    $new_json = $new_values ? json_encode($new_values) : null;
    
    $stmt = $conn->prepare("INSERT INTO user_activity_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("ississs", $_SESSION['user_id'], $action, $table_name, $record_id, $old_json, $new_json, $ip_address, $user_agent);
    $stmt->execute();
}

// Function to get user's records filter for SQL queries
function getUserRecordsFilter() {
    if (isAdmin()) {
        return ""; // Admin can see all records
    } else {
        return " AND user_id = " . $_SESSION['user_id']; // Users can only see their own records
    }
}

// Function to check record ownership
function canAccessRecord($table, $record_id) {
    global $conn;
    
    if (isAdmin()) {
        return true; // Admin can access all records
    }
    
    $stmt = $conn->prepare("SELECT user_id FROM $table WHERE id = ?");
    $stmt->bind_param("i", $record_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['user_id'] == $_SESSION['user_id'];
    }
    
    return false;
}

// Set security headers
header("X-Frame-Options: DENY");
header("X-XSS-Protection: 1; mode=block");
header("X-Content-Type-Options: nosniff");
?>
