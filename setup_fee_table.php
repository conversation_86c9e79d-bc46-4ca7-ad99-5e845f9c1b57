<?php
// Include database connection
include_once 'db_connection.php';

echo "<h2>Fee Table Setup</h2>";

try {
    // Check if fee table exists
    $check_table = "SHOW TABLES LIKE 'fee'";
    $result = $conn->query($check_table);
    
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✓ Fee table already exists</p>";
        
        // Show table structure
        $describe = "DESCRIBE fee";
        $desc_result = $conn->query($describe);
        
        echo "<h3>Current Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        while ($row = $desc_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p style='color: orange;'>⚠ Fee table does not exist. Creating...</p>";
        
        // Create fee table
        $create_table_sql = "CREATE TABLE fee (
            id INT AUTO_INCREMENT PRIMARY KEY,
            application_type VARCHAR(100) NOT NULL,
            priority VARCHAR(50) NOT NULL,
            fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            service_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            total_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_fee (application_type, priority)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($create_table_sql)) {
            echo "<p style='color: green;'>✓ Fee table created successfully!</p>";
        } else {
            throw new Exception("Error creating fee table: " . $conn->error);
        }
    }
    
    // Check if lookup tables exist
    echo "<h3>Lookup Tables Status:</h3>";
    
    $tables_to_check = ['application_types', 'priorities'];
    
    foreach ($tables_to_check as $table) {
        $check_table = "SHOW TABLES LIKE '$table'";
        $result = $conn->query($check_table);
        
        if ($result->num_rows > 0) {
            echo "<p style='color: green;'>✓ Table '$table' exists</p>";
            
            // Count records
            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count_row = $count_result->fetch_assoc();
            echo "<p style='margin-left: 20px;'>Records: " . $count_row['count'] . "</p>";
            
        } else {
            echo "<p style='color: red;'>✗ Table '$table' does not exist</p>";
            echo "<p style='margin-left: 20px;'><a href='create_lookup_tables.php'>Click here to create lookup tables</a></p>";
        }
    }
    
    // Show sample data from fee table if it exists
    $fee_data = $conn->query("SELECT * FROM fee LIMIT 5");
    if ($fee_data && $fee_data->num_rows > 0) {
        echo "<h3>Sample Fee Records:</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Application Type</th><th>Priority</th><th>Fee</th><th>Service Fee</th><th>Total Fee</th></tr>";
        
        while ($row = $fee_data->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['application_type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['priority']) . "</td>";
            echo "<td>₨ " . number_format($row['fee'], 2) . "</td>";
            echo "<td>₨ " . number_format($row['service_fee'], 2) . "</td>";
            echo "<td>₨ " . number_format($row['total_fee'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<h3>No fee records found</h3>";
        echo "<p><a href='fee.php'>Add your first fee record</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
} finally {
    $conn->close();
}

echo "<hr>";
echo "<h3>Quick Links:</h3>";
echo "<p>";
echo "<a href='fee.php' style='margin-right: 15px;'>Manage Fees</a>";
echo "<a href='manage_lookups.php' style='margin-right: 15px;'>Manage Lookups</a>";
echo "<a href='create_lookup_tables.php' style='margin-right: 15px;'>Create Lookup Tables</a>";
echo "<a href='index.php'>Home</a>";
echo "</p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f5f5f5;
}

table {
    background: white;
    padding: 10px;
}

th {
    background: #2c5530;
    color: white;
    padding: 8px;
}

td {
    padding: 8px;
}

a {
    color: #2c5530;
    text-decoration: none;
    padding: 8px 15px;
    background: #e3f2fd;
    border-radius: 5px;
    display: inline-block;
    margin: 5px;
}

a:hover {
    background: #bbdefb;
}
</style>
