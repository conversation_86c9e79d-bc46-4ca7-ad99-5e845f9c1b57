<?php
// Include database connection
include_once 'db_connection.php';

// Variables to store results
$fee_table_exists = false;
$table_structure = [];
$lookup_tables_status = [];
$sample_fee_records = [];
$messages = [];

try {
    // Check if fee table exists
    $check_table = "SHOW TABLES LIKE 'fee'";
    $result = $conn->query($check_table);

    if ($result->num_rows > 0) {
        $fee_table_exists = true;
        $messages[] = ['type' => 'success', 'text' => 'Fee table already exists'];

        // Show table structure
        $describe = "DESCRIBE fee";
        $desc_result = $conn->query($describe);

        while ($row = $desc_result->fetch_assoc()) {
            $table_structure[] = $row;
        }

    } else {
        $messages[] = ['type' => 'warning', 'text' => 'Fee table does not exist. Creating...'];

        // Create fee table
        $create_table_sql = "CREATE TABLE fee (
            id INT AUTO_INCREMENT PRIMARY KEY,
            application_type VARCHAR(100) NOT NULL,
            priority VARCHAR(50) NOT NULL,
            fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            service_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            total_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_fee (application_type, priority)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if ($conn->query($create_table_sql)) {
            $fee_table_exists = true;
            $messages[] = ['type' => 'success', 'text' => 'Fee table created successfully!'];
        } else {
            throw new Exception("Error creating fee table: " . $conn->error);
        }
    }

    // Check if lookup tables exist
    $tables_to_check = ['application_types', 'priorities'];

    foreach ($tables_to_check as $table) {
        $check_table = "SHOW TABLES LIKE '$table'";
        $result = $conn->query($check_table);

        if ($result->num_rows > 0) {
            // Count records
            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count_row = $count_result->fetch_assoc();

            $lookup_tables_status[] = [
                'table' => $table,
                'exists' => true,
                'count' => $count_row['count']
            ];
        } else {
            $lookup_tables_status[] = [
                'table' => $table,
                'exists' => false,
                'count' => 0
            ];
        }
    }

    // Show sample data from fee table if it exists
    if ($fee_table_exists) {
        $fee_data = $conn->query("SELECT * FROM fee LIMIT 5");
        if ($fee_data && $fee_data->num_rows > 0) {
            while ($row = $fee_data->fetch_assoc()) {
                $sample_fee_records[] = $row;
            }
        }
    }

} catch (Exception $e) {
    $messages[] = ['type' => 'error', 'text' => 'Error: ' . $e->getMessage()];
} finally {
    $conn->close();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Fee Table - Pakistan Identity Card System</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5530;
            --secondary-color: #4a7c59;
            --accent-color: #f8f9fa;
            --text-dark: #2d3436;
            --border-color: #e9ecef;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header-section h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .header-section .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-top: 10px;
            position: relative;
            z-index: 1;
        }

        .content-section {
            padding: 40px;
        }

        .section-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 20px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .table th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            border: none;
            padding: 15px;
        }

        .table td {
            padding: 12px 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }

        .status-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .quick-links {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .btn-link {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 5px;
        }

        .btn-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(44, 85, 48, 0.3);
            color: white;
            text-decoration: none;
        }

        .btn-secondary-link {
            background: #6c757d;
        }

        .btn-secondary-link:hover {
            background: #5a6268;
            box-shadow: 0 10px 25px rgba(108, 117, 125, 0.3);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header-section {
                padding: 20px;
            }

            .header-section h1 {
                font-size: 2rem;
            }

            .content-section {
                padding: 25px;
            }

            .section-card {
                padding: 20px;
            }

            .btn-link {
                width: 100%;
                margin: 5px 0;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .header-section h1 {
                font-size: 1.8rem;
            }

            .content-section {
                padding: 20px;
            }

            .section-card {
                padding: 15px;
            }
        }

        /* Animation */
        .section-card, .quick-links {
            animation: slideInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .section-card:nth-child(1) { animation-delay: 0.1s; }
        .section-card:nth-child(2) { animation-delay: 0.2s; }
        .section-card:nth-child(3) { animation-delay: 0.3s; }
        .quick-links { animation-delay: 0.4s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f5f5f5;
}

table {
    background: white;
    padding: 10px;
}

th {
    background: #2c5530;
    color: white;
    padding: 8px;
}

td {
    padding: 8px;
}

a {
    color: #2c5530;
    text-decoration: none;
    padding: 8px 15px;
    background: #e3f2fd;
    border-radius: 5px;
    display: inline-block;
    margin: 5px;
}

a:hover {
    background: #bbdefb;
}
</style>
