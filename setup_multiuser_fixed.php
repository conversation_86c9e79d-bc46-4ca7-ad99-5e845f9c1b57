<?php
// Fixed Multi-User System Setup
require_once 'db_connection.php';

echo "<h2>Setting up Multi-User System (Fixed Version)...</h2>";

// First, let's check the current structure of login table
echo "<h3>Checking existing table structure...</h3>";

$result = $conn->query("DESCRIBE login");
if ($result) {
    echo "<strong>Current login table structure:</strong><br>";
    while ($row = $result->fetch_assoc()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")<br>";
    }
    echo "<br>";
}

// 1. Update login table to include roles and user management
echo "<h3>Step 1: Updating login table...</h3>";
$sql_updates = [
    "ALTER TABLE login ADD COLUMN IF NOT EXISTS role ENUM('admin', 'user') DEFAULT 'user'",
    "ALTER TABLE login ADD COLUMN IF NOT EXISTS full_name VARCHAR(100) DEFAULT NULL",
    "ALTER TABLE login ADD COLUMN IF NOT EXISTS email VARCHAR(100) DEFAULT NULL", 
    "ALTER TABLE login ADD COLUMN IF NOT EXISTS phone VARCHAR(15) DEFAULT NULL",
    "ALTER TABLE login ADD COLUMN IF NOT EXISTS status ENUM('active', 'inactive') DEFAULT 'active'",
    "ALTER TABLE login ADD COLUMN IF NOT EXISTS created_by INT DEFAULT NULL",
    "ALTER TABLE login ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
    "ALTER TABLE login ADD COLUMN IF NOT EXISTS last_login TIMESTAMP NULL DEFAULT NULL"
];

foreach ($sql_updates as $sql) {
    if ($conn->query($sql)) {
        echo "✓ " . substr($sql, 0, 50) . "...<br>";
    } else {
        echo "⚠ " . substr($sql, 0, 50) . "... (might already exist)<br>";
    }
}

// 2. Update tokenslip table
echo "<h3>Step 2: Updating tokenslip table...</h3>";
$sql_tokenslip_updates = [
    "ALTER TABLE tokenslip ADD COLUMN IF NOT EXISTS user_id INT DEFAULT NULL",
    "ALTER TABLE tokenslip ADD INDEX IF NOT EXISTS idx_user_id (user_id)"
];

foreach ($sql_tokenslip_updates as $sql) {
    if ($conn->query($sql)) {
        echo "✓ " . substr($sql, 0, 50) . "...<br>";
    } else {
        echo "⚠ " . substr($sql, 0, 50) . "... (might already exist)<br>";
    }
}

// 3. Update emails table
echo "<h3>Step 3: Updating emails table...</h3>";
$sql_emails_updates = [
    "ALTER TABLE emails ADD COLUMN IF NOT EXISTS user_id INT DEFAULT NULL",
    "ALTER TABLE emails ADD COLUMN IF NOT EXISTS status ENUM('active', 'inactive') DEFAULT 'active'",
    "ALTER TABLE emails ADD INDEX IF NOT EXISTS idx_user_id (user_id)"
];

foreach ($sql_emails_updates as $sql) {
    if ($conn->query($sql)) {
        echo "✓ " . substr($sql, 0, 50) . "...<br>";
    } else {
        echo "⚠ " . substr($sql, 0, 50) . "... (might already exist)<br>";
    }
}

// 4. Create user_permissions table
echo "<h3>Step 4: Creating user_permissions table...</h3>";
$sql_permissions = "
CREATE TABLE IF NOT EXISTS user_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    permission VARCHAR(50) NOT NULL,
    granted_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_permission (user_id, permission),
    INDEX idx_user_id (user_id),
    INDEX idx_granted_by (granted_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
";

if ($conn->query($sql_permissions)) {
    echo "✓ User permissions table created successfully<br>";
} else {
    echo "⚠ User permissions table: " . $conn->error . "<br>";
}

// 5. Create password_reset_tokens table
echo "<h3>Step 5: Creating password_reset_tokens table...</h3>";
$sql_reset_tokens = "
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_token (token),
    INDEX idx_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
";

if ($conn->query($sql_reset_tokens)) {
    echo "✓ Password reset tokens table created successfully<br>";
} else {
    echo "⚠ Password reset tokens table: " . $conn->error . "<br>";
}

// 6. Create user_activity_logs table
echo "<h3>Step 6: Creating user_activity_logs table...</h3>";
$sql_activity_logs = "
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50) DEFAULT NULL,
    record_id INT DEFAULT NULL,
    old_values TEXT DEFAULT NULL,
    new_values TEXT DEFAULT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_user_action (user_id, action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
";

if ($conn->query($sql_activity_logs)) {
    echo "✓ User activity logs table created successfully<br>";
} else {
    echo "⚠ User activity logs table: " . $conn->error . "<br>";
}

// 7. Create or update admin user
echo "<h3>Step 7: Setting up admin user...</h3>";
$admin_username = 'admin';
$admin_password = password_hash('admin123', PASSWORD_DEFAULT);

// Check if admin exists
$stmt = $conn->prepare("SELECT id FROM login WHERE username = ?");
$stmt->bind_param("s", $admin_username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    // Update existing admin
    $admin_row = $result->fetch_assoc();
    $admin_id = $admin_row['id'];
    
    $stmt = $conn->prepare("UPDATE login SET role = 'admin', full_name = 'System Administrator', status = 'active' WHERE id = ?");
    $stmt->bind_param("i", $admin_id);
    $stmt->execute();
    echo "✓ Admin user updated successfully (ID: $admin_id)<br>";
} else {
    // Create new admin
    $stmt = $conn->prepare("INSERT INTO login (username, password, role, full_name, status) VALUES (?, ?, 'admin', 'System Administrator', 'active')");
    $stmt->bind_param("ss", $admin_username, $admin_password);
    $stmt->execute();
    $admin_id = $conn->insert_id;
    echo "✓ Admin user created successfully (ID: $admin_id)<br>";
}

// 8. Grant permissions to admin
echo "<h3>Step 8: Setting up admin permissions...</h3>";
$admin_permissions = [
    'view_all_records',
    'edit_all_records', 
    'delete_all_records',
    'manage_users',
    'view_reports',
    'system_settings',
    'view_logs'
];

foreach ($admin_permissions as $permission) {
    $stmt = $conn->prepare("INSERT IGNORE INTO user_permissions (user_id, permission, granted_by) VALUES (?, ?, ?)");
    $stmt->bind_param("isi", $admin_id, $permission, $admin_id);
    if ($stmt->execute()) {
        echo "✓ Permission '$permission' granted to admin<br>";
    }
}

// 9. Create sample user
echo "<h3>Step 9: Creating sample user...</h3>";
$user_username = 'user1';
$user_password = password_hash('user123', PASSWORD_DEFAULT);

$stmt = $conn->prepare("SELECT id FROM login WHERE username = ?");
$stmt->bind_param("s", $user_username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    $stmt = $conn->prepare("INSERT INTO login (username, password, role, full_name, status, created_by) VALUES (?, ?, 'user', 'Sample User', 'active', ?)");
    $stmt->bind_param("ssi", $user_username, $user_password, $admin_id);
    $stmt->execute();
    $user_id = $conn->insert_id;
    
    // Grant basic permissions to user
    $user_permissions = ['view_own_records', 'edit_own_records'];
    foreach ($user_permissions as $permission) {
        $stmt = $conn->prepare("INSERT INTO user_permissions (user_id, permission, granted_by) VALUES (?, ?, ?)");
        $stmt->bind_param("isi", $user_id, $permission, $admin_id);
        $stmt->execute();
    }
    
    echo "✓ Sample user created successfully (ID: $user_id)<br>";
} else {
    echo "✓ Sample user already exists<br>";
}

// 10. Final verification
echo "<h3>Step 10: Final verification...</h3>";

// Check tables exist
$tables_to_check = ['login', 'user_permissions', 'password_reset_tokens', 'user_activity_logs'];
foreach ($tables_to_check as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "✓ Table '$table' exists<br>";
    } else {
        echo "✗ Table '$table' missing<br>";
    }
}

// Check users exist
$result = $conn->query("SELECT username, role FROM login WHERE status = 'active'");
echo "<br><strong>Active users in system:</strong><br>";
while ($row = $result->fetch_assoc()) {
    echo "- " . $row['username'] . " (" . $row['role'] . ")<br>";
}

echo "<br><h3>🎉 Setup Complete!</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>Login Credentials:</h4>";
echo "<p><strong>Admin Login:</strong><br>";
echo "URL: <a href='login_multiuser.php'>login_multiuser.php</a><br>";
echo "Username: <code>admin</code><br>";
echo "Password: <code>admin123</code></p>";

echo "<p><strong>Sample User Login:</strong><br>";
echo "Username: <code>user1</code><br>";
echo "Password: <code>user123</code></p>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>⚠️ Important:</h4>";
echo "<ul>";
echo "<li>Change default passwords immediately</li>";
echo "<li>Test both admin and user logins</li>";
echo "<li>Admin can see all records, users only their own</li>";
echo "<li>Use <code>login_multiuser.php</code> for new login system</li>";
echo "</ul>";
echo "</div>";

$conn->close();
?>
