<?php
// Multi-User System Setup
require_once 'db_connection.php';

echo "<h2>Setting up Multi-User System...</h2>";

// 1. Update login table to include roles and user management
$sql_update_login = "
ALTER TABLE login 
ADD COLUMN IF NOT EXISTS role ENUM('admin', 'user') DEFAULT 'user',
ADD COLUMN IF NOT EXISTS full_name VARCHAR(100) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS email VARCHAR(100) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS phone VARCHAR(15) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS status ENUM('active', 'inactive') DEFAULT 'active',
ADD COLUMN IF NOT EXISTS created_by INT DEFAULT NULL,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS last_login TIMESTAMP NULL DEFAULT NULL
";

if ($conn->query($sql_update_login)) {
    echo "✓ Login table updated successfully<br>";
} else {
    echo "Error updating login table: " . $conn->error . "<br>";
}

// 2. Add user_id column to tokenslip table for record ownership
$sql_update_tokenslip = "
ALTER TABLE tokenslip 
ADD COLUMN IF NOT EXISTS user_id INT DEFAULT NULL,
ADD INDEX IF NOT EXISTS idx_user_id (user_id)
";

if ($conn->query($sql_update_tokenslip)) {
    echo "✓ Tokenslip table updated successfully<br>";
} else {
    echo "Error updating tokenslip table: " . $conn->error . "<br>";
}

// 3. Add user_id column to emails table for record ownership
$sql_update_emails = "
ALTER TABLE emails 
ADD COLUMN IF NOT EXISTS user_id INT DEFAULT NULL,
ADD COLUMN IF NOT EXISTS status ENUM('active', 'inactive') DEFAULT 'active',
ADD INDEX IF NOT EXISTS idx_user_id (user_id)
";

if ($conn->query($sql_update_emails)) {
    echo "✓ Emails table updated successfully<br>";
} else {
    echo "Error updating emails table: " . $conn->error . "<br>";
}

// 4. Create user_permissions table (without foreign keys first)
$sql_permissions = "
CREATE TABLE IF NOT EXISTS user_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    permission VARCHAR(50) NOT NULL,
    granted_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_permission (user_id, permission),
    INDEX idx_user_id (user_id),
    INDEX idx_granted_by (granted_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
";

if ($conn->query($sql_permissions)) {
    echo "✓ User permissions table created successfully<br>";
} else {
    echo "Error creating permissions table: " . $conn->error . "<br>";
}

// 5. Create password_reset_tokens table (without foreign keys first)
$sql_reset_tokens = "
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_token (token),
    INDEX idx_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
";

if ($conn->query($sql_reset_tokens)) {
    echo "✓ Password reset tokens table created successfully<br>";
} else {
    echo "Error creating reset tokens table: " . $conn->error . "<br>";
}

// 6. Create user_activity_logs table (without foreign keys first)
$sql_activity_logs = "
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50) DEFAULT NULL,
    record_id INT DEFAULT NULL,
    old_values JSON DEFAULT NULL,
    new_values JSON DEFAULT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_user_action (user_id, action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
";

if ($conn->query($sql_activity_logs)) {
    echo "✓ User activity logs table created successfully<br>";
} else {
    echo "Error creating activity logs table: " . $conn->error . "<br>";
}

// 7. Update existing admin user or create if not exists
$admin_username = 'admin';
$admin_password = password_hash('admin123', PASSWORD_DEFAULT);

// Check if admin exists
$stmt = $conn->prepare("SELECT id FROM login WHERE username = ?");
$stmt->bind_param("s", $admin_username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    // Update existing admin
    $admin_row = $result->fetch_assoc();
    $admin_id = $admin_row['id'];
    
    $stmt = $conn->prepare("UPDATE login SET role = 'admin', full_name = 'System Administrator', status = 'active' WHERE id = ?");
    $stmt->bind_param("i", $admin_id);
    $stmt->execute();
    echo "✓ Admin user updated successfully<br>";
} else {
    // Create new admin
    $stmt = $conn->prepare("INSERT INTO login (username, password, role, full_name, status) VALUES (?, ?, 'admin', 'System Administrator', 'active')");
    $stmt->bind_param("ss", $admin_username, $admin_password);
    $stmt->execute();
    $admin_id = $conn->insert_id;
    echo "✓ Admin user created successfully<br>";
}

// 8. Grant all permissions to admin
$admin_permissions = [
    'view_all_records',
    'edit_all_records',
    'delete_all_records',
    'manage_users',
    'view_reports',
    'system_settings',
    'view_logs'
];

foreach ($admin_permissions as $permission) {
    $stmt = $conn->prepare("INSERT IGNORE INTO user_permissions (user_id, permission, granted_by) VALUES (?, ?, ?)");
    $stmt->bind_param("isi", $admin_id, $permission, $admin_id);
    $stmt->execute();
}

echo "✓ Admin permissions granted successfully<br>";

// 9. Create sample regular user
$user_username = 'user1';
$user_password = password_hash('user123', PASSWORD_DEFAULT);

$stmt = $conn->prepare("SELECT id FROM login WHERE username = ?");
$stmt->bind_param("s", $user_username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    $stmt = $conn->prepare("INSERT INTO login (username, password, role, full_name, status, created_by) VALUES (?, ?, 'user', 'Sample User', 'active', ?)");
    $stmt->bind_param("ssi", $user_username, $user_password, $admin_id);
    $stmt->execute();
    $user_id = $conn->insert_id;
    
    // Grant basic permissions to user
    $user_permissions = ['view_own_records', 'edit_own_records'];
    foreach ($user_permissions as $permission) {
        $stmt = $conn->prepare("INSERT INTO user_permissions (user_id, permission, granted_by) VALUES (?, ?, ?)");
        $stmt->bind_param("isi", $user_id, $permission, $admin_id);
        $stmt->execute();
    }
    
    echo "✓ Sample user created successfully<br>";
}

echo "<br><h3>Setup Complete!</h3>";
echo "<p><strong>Admin Login:</strong><br>";
echo "Username: admin<br>";
echo "Password: admin123</p>";

echo "<p><strong>Sample User Login:</strong><br>";
echo "Username: user1<br>";
echo "Password: user123</p>";

echo "<p><strong>Features Added:</strong></p>";
echo "<ul>";
echo "<li>Role-based access control (Admin/User)</li>";
echo "<li>User management system</li>";
echo "<li>Record ownership (users can only see their own records)</li>";
echo "<li>Password management</li>";
echo "<li>Activity logging</li>";
echo "<li>Permission system</li>";
echo "</ul>";

$conn->close();
?>
