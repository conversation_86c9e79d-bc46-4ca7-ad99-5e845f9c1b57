<?php
require_once 'session_multiuser.php';

// Get user's statistics
$user_id = $_SESSION['user_id'];

// User's total records
$result = $conn->query("SELECT COUNT(*) as total FROM tokenslip WHERE user_id = $user_id");
$user_stats['total_records'] = $result->fetch_assoc()['total'];

// User's today's records
$result = $conn->query("SELECT COUNT(*) as total FROM tokenslip WHERE user_id = $user_id AND DATE(created_at) = CURDATE()");
$user_stats['today_records'] = $result->fetch_assoc()['total'];

// User's pending records
$result = $conn->query("SELECT COUNT(*) as total FROM tokenslip WHERE user_id = $user_id AND status = 'Pending'");
$user_stats['pending_records'] = $result->fetch_assoc()['total'];

// User's completed records
$result = $conn->query("SELECT COUNT(*) as total FROM tokenslip WHERE user_id = $user_id AND status = 'Completed'");
$user_stats['completed_records'] = $result->fetch_assoc()['total'];

// User's recent records
$recent_records = $conn->query("
    SELECT id, name, cnic_number, application_type, status, created_at 
    FROM tokenslip 
    WHERE user_id = $user_id 
    ORDER BY created_at DESC 
    LIMIT 5
");

// User's recent activities
$recent_activities = $conn->query("
    SELECT action, table_name, record_id, created_at 
    FROM user_activity_logs 
    WHERE user_id = $user_id 
    ORDER BY created_at DESC 
    LIMIT 5
");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Dashboard - Pakistan Identity System</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5530;
            --secondary-color: #4a7c59;
            --accent-color: #f8f9fa;
            --text-dark: #2d3436;
            --border-color: #e9ecef;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 30px;
            position: relative;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header-content {
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-content h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
        }

        .user-info {
            text-align: right;
        }

        .user-info h5 {
            margin: 0;
            font-weight: 600;
        }

        .user-info small {
            opacity: 0.9;
        }

        .content-section {
            padding: 40px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            border-left: 4px solid;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.primary { border-left-color: var(--primary-color); }
        .stat-card.success { border-left-color: var(--success-color); }
        .stat-card.warning { border-left-color: var(--warning-color); }
        .stat-card.info { border-left-color: var(--info-color); }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 15px;
        }

        .stat-icon.primary { background: var(--primary-color); }
        .stat-icon.success { background: var(--success-color); }
        .stat-icon.warning { background: var(--warning-color); }
        .stat-icon.info { background: var(--info-color); }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-dark);
            margin: 0;
        }

        .stat-label {
            color: #6c757d;
            font-weight: 500;
            margin: 0;
        }

        .section-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 20px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .action-btn {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 12px;
            text-decoration: none;
            text-align: center;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(44, 85, 48, 0.3);
            color: white;
            text-decoration: none;
        }

        .record-item {
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .record-item:last-child {
            border-bottom: none;
        }

        .record-info h6 {
            margin: 0;
            font-weight: 600;
            color: var(--text-dark);
        }

        .record-info small {
            color: #6c757d;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-pending {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-completed {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-processing {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .navigation-links {
            margin-bottom: 20px;
        }

        .navigation-links a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            margin-right: 15px;
            padding: 8px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .navigation-links a:hover {
            background: var(--accent-color);
            color: var(--secondary-color);
            text-decoration: none;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }

            .header-section {
                padding: 20px;
            }

            .header-content {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .header-content h1 {
                font-size: 2rem;
            }

            .content-section {
                padding: 25px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }
        }

        /* Footer Styles */
        .footer-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            margin-top: 30px;
        }

        .footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links li {
            margin-bottom: 8px;
        }

        .footer-links a {
            color: #666;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--primary-color);
            text-decoration: none;
        }

        .footer-links h6 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 15px;
            font-size: 1rem;
        }

        .footer-divider {
            border-color: #eee;
            margin: 25px 0 15px 0;
        }

        /* Animation */
        .stat-card, .section-card {
            animation: slideInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .stat-card:nth-child(2) { animation-delay: 0.2s; }
        .stat-card:nth-child(3) { animation-delay: 0.3s; }
        .stat-card:nth-child(4) { animation-delay: 0.4s; }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="header-content">
                <div>
                    <h1><i class="fas fa-user-circle me-3"></i>My Dashboard</h1>
                    <p class="subtitle mb-0">Pakistan Identity Card Management</p>
                </div>
                <div class="user-info">
                    <h5>Welcome, <?php echo htmlspecialchars($_SESSION['full_name']); ?></h5>
                    <small><i class="fas fa-user me-1"></i>User Account</small>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <!-- Navigation -->
            <div class="navigation-links">
                <a href="index.php"><i class="fas fa-plus me-1"></i>Add New Record</a>
                <a href="my_records.php"><i class="fas fa-table me-1"></i>My Records</a>
                <a href="change_password.php"><i class="fas fa-key me-1"></i>Change Password</a>
                <a href="logout.php"><i class="fas fa-sign-out-alt me-1"></i>Logout</a>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon primary">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="stat-number"><?php echo $user_stats['total_records']; ?></h3>
                    <p class="stat-label">Total Records</p>
                </div>

                <div class="stat-card warning">
                    <div class="stat-icon warning">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <h3 class="stat-number"><?php echo $user_stats['today_records']; ?></h3>
                    <p class="stat-label">Today's Records</p>
                </div>

                <div class="stat-card info">
                    <div class="stat-icon info">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="stat-number"><?php echo $user_stats['pending_records']; ?></h3>
                    <p class="stat-label">Pending Records</p>
                </div>

                <div class="stat-card success">
                    <div class="stat-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="stat-number"><?php echo $user_stats['completed_records']; ?></h3>
                    <p class="stat-label">Completed Records</p>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-bolt"></i>
                    Quick Actions
                </h3>
                <div class="quick-actions">
                    <a href="index.php" class="action-btn">
                        <i class="fas fa-plus-circle"></i>
                        Add New Application
                    </a>
                    <a href="my_records.php" class="action-btn">
                        <i class="fas fa-search"></i>
                        View My Records
                    </a>
                    <a href="change_password.php" class="action-btn">
                        <i class="fas fa-key"></i>
                        Change Password
                    </a>
                    <a href="my_records.php?status=pending" class="action-btn">
                        <i class="fas fa-clock"></i>
                        Check Status
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Recent Records -->
                <div class="col-lg-8">
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-history"></i>
                            Recent Records
                        </h3>
                        <div class="records-list">
                            <?php if ($recent_records->num_rows > 0): ?>
                                <?php while ($record = $recent_records->fetch_assoc()): ?>
                                <div class="record-item">
                                    <div class="record-info">
                                        <h6><?php echo htmlspecialchars($record['name']); ?></h6>
                                        <small>
                                            CNIC: <?php echo htmlspecialchars($record['cnic_number']); ?> |
                                            Type: <?php echo htmlspecialchars($record['application_type']); ?>
                                            <br>
                                            Created: <?php echo date('M d, Y H:i', strtotime($record['created_at'])); ?>
                                        </small>
                                    </div>
                                    <div>
                                        <span class="status-badge status-<?php echo strtolower($record['status']); ?>">
                                            <?php echo $record['status']; ?>
                                        </span>
                                    </div>
                                </div>
                                <?php endwhile; ?>
                                <div class="text-center mt-3">
                                    <a href="my_records.php" class="btn btn-outline-primary">
                                        <i class="fas fa-eye me-2"></i>View All Records
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-file-alt fa-3x mb-3"></i>
                                    <h5>No Records Found</h5>
                                    <p>You haven't created any records yet.</p>
                                    <a href="index.php" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Create Your First Record
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Account Info & Recent Activities -->
                <div class="col-lg-4">
                    <!-- Account Information -->
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-user-cog"></i>
                            Account Information
                        </h3>
                        <div class="account-info">
                            <div class="info-item mb-3">
                                <strong>Username:</strong><br>
                                <span class="text-muted"><?php echo htmlspecialchars($_SESSION['username']); ?></span>
                            </div>
                            <div class="info-item mb-3">
                                <strong>Full Name:</strong><br>
                                <span class="text-muted"><?php echo htmlspecialchars($_SESSION['full_name']); ?></span>
                            </div>
                            <div class="info-item mb-3">
                                <strong>Role:</strong><br>
                                <span class="badge bg-primary"><?php echo ucfirst($_SESSION['role']); ?></span>
                            </div>
                            <div class="info-item">
                                <strong>Last Login:</strong><br>
                                <span class="text-muted">
                                    <?php
                                    $last_login = $conn->query("SELECT last_login FROM login WHERE id = " . $_SESSION['user_id'])->fetch_assoc()['last_login'];
                                    echo $last_login ? date('M d, Y H:i', strtotime($last_login)) : 'First time login';
                                    ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-clock"></i>
                            Recent Activities
                        </h3>
                        <div class="activities-list">
                            <?php if ($recent_activities->num_rows > 0): ?>
                                <?php while ($activity = $recent_activities->fetch_assoc()): ?>
                                <div class="activity-item">
                                    <div class="activity-content">
                                        <h6>
                                            <?php
                                            $action_text = str_replace('_', ' ', $activity['action']);
                                            echo ucfirst($action_text);
                                            ?>
                                        </h6>
                                        <small>
                                            <?php echo date('M d, Y H:i', strtotime($activity['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-clock fa-2x mb-2"></i>
                                    <p>No recent activities.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <!-- User Footer Navigation -->
        <footer class="mt-5">
            <div class="footer-card">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-user-circle me-2"></i>My Account</h6>
                        <ul class="footer-links">
                            <li><a href="user_dashboard.php">My Dashboard</a></li>
                            <li><a href="my_records.php">My Records</a></li>
                            <li><a href="change_password.php">Change Password</a></li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-file-alt me-2"></i>Records</h6>
                        <ul class="footer-links">
                            <li><a href="index.php">Add New Record</a></li>
                            <li><a href="my_records.php?status=pending">Pending Records</a></li>
                            <li><a href="my_records.php?status=completed">Completed Records</a></li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-info-circle me-2"></i>Help & Support</h6>
                        <ul class="footer-links">
                            <li><a href="#" onclick="alert('Contact admin for support')">Contact Support</a></li>
                            <li><a href="#" onclick="alert('User guide coming soon')">User Guide</a></li>
                            <li><a href="logout.php">Logout</a></li>
                        </ul>
                    </div>
                </div>
                <hr class="footer-divider">
                <div class="text-center">
                    <small class="text-muted">
                        &copy; <?php echo date('Y'); ?> Pakistan Identity Card Management System - User Panel
                    </small>
                </div>
            </div>
        </footer>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Add smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.stat-card, .section-card');
            elements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    el.style.transition = 'all 0.6s ease';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // Real-time clock
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();

            // You can add a clock element if needed
            console.log(`Current time: ${timeString}`);
        }

        setInterval(updateClock, 1000);
    </script>
</body>
</html>
